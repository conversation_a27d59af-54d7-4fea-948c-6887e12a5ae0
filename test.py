#!/usr/bin/env python
# -*- coding: utf-8 -*-

from yade import *
import random

# 清除之前的模拟
O.reset()

# 设置周期性边界条件
O.periodic = True

# 定义周期性单元尺寸 (单位：米)
cell_size = (100e-6, 100e-6, 200e-6)  # 100微米 x 100微米 x 200微米
O.cell.hSize = Matrix3(
    cell_size[0], 0, 0,
    0, cell_size[1], 0,
    0, 0, cell_size[2]
)

# 材料属性
mat = FrictMat(
    young=1e6,        # 杨氏模量 1 MPa
    poisson=0.3,      # 泊松比
    frictionAngle=0.5, # 摩擦角
    density=2650      # 密度 kg/m³
)
O.materials.append(mat)

# 不使用地面，而是通过重力和周期性边界条件观察堆积
# 在周期性边界条件下，不能使用超过单元尺寸一半的物体

# 颗粒参数
particle_radius = 2.5e-6  # 2.5微米半径 = 5微米直径
num_particles = 200
particles_created = 0

# 随机种子，确保可重复性
random.seed(42)

# 生成不重叠的随机颗粒位置
def generate_random_position():
    """生成随机位置，确保不与现有颗粒重叠"""
    max_attempts = 1000
    min_distance = 2.2 * particle_radius  # 最小间距

    for attempt in range(max_attempts):
        # 在计算域内随机生成位置
        x = random.uniform(particle_radius, cell_size[0] - particle_radius)
        y = random.uniform(particle_radius, cell_size[1] - particle_radius)
        z = random.uniform(particle_radius + 1e-6, cell_size[2] * 0.8)  # 在上半部分生成

        new_pos = Vector3(x, y, z)

        # 检查与现有颗粒的距离
        valid_position = True
        for body in O.bodies:
            if isinstance(body.shape, Sphere):
                existing_pos = body.state.pos
                distance = (new_pos - existing_pos).norm()
                if distance < min_distance:
                    valid_position = False
                    break

        if valid_position:
            return new_pos

    # 如果无法找到合适位置，返回None
    return None

print("开始生成200个随机分布的颗粒...")

# 创建200个随机分布的颗粒
while particles_created < num_particles:
    pos = generate_random_position()
    if pos is None:
        print(f"警告：无法为第{particles_created + 1}个颗粒找到合适位置")
        break

    # 创建颗粒
    sphere = utils.sphere(
        center=pos,
        radius=particle_radius,
        material=mat
    )
    O.bodies.append(sphere)

    # 给颗粒一个小的随机初始速度
    sphere_body = O.bodies[-1]
    sphere_body.state.vel = Vector3(
        random.uniform(-1e-4, 1e-4),  # 小的随机x速度
        random.uniform(-1e-4, 1e-4),  # 小的随机y速度
        random.uniform(-1e-4, 0)      # 小的向下z速度
    )

    particles_created += 1
    if particles_created % 50 == 0:
        print(f"已创建 {particles_created} 个颗粒")

print(f"成功创建了 {particles_created} 个颗粒")

# 引擎设置
O.engines = [
    # 重置力
    ForceResetter(),

    # 碰撞检测器 - 支持周期性边界条件
    InsertionSortCollider([Bo1_Sphere_Aabb()]),

    # 相互作用循环
    InteractionLoop(
        # 几何接触检测
        [
            Ig2_Sphere_Sphere_ScGeom()
        ],
        # 物理属性计算
        [
            Ip2_FrictMat_FrictMat_FrictPhys()
        ],
        # 接触定律
        [
            Law2_ScGeom_FrictPhys_CundallStrack()
        ]
    ),

    # 牛顿积分器
    NewtonIntegrator(
        gravity=(0, 0, -9.81),  # 标准重力加速度
        damping=0.2             # 适度阻尼，帮助稳定
    )
]

# 设置时间步长（根据颗粒大小和材料属性自动计算）
O.dt = PWaveTimeStep() * 0.5  # 使用P波时间步长的一半，确保稳定性

# 启动Qt界面
from yade import qt
qt.Controller()
qt.View()

# 测试说明：
# 1. 创建了200个随机分布的颗粒（直径5μm）在100×100×200μm的周期性计算域中
# 2. 没有地面约束（在周期性边界条件下不能使用大型边界）
# 3. 启用了重力（-9.81 m/s²）和阻尼
# 4. XYZ方向都是周期性边界条件
# 5. 观察要点：
#    - 颗粒在重力作用下的运动行为
#    - 颗粒是否能在XYZ方向正确穿越周期性边界
#    - 颗粒间的接触和碰撞是否正常
#    - 在周期性边界条件下的集体运动行为
#    - 注意：颗粒会在重力作用下向下运动，并从底部边界重新出现在顶部

print("=== 重力堆积测试脚本已加载 ===")
print(f"颗粒数量: {particles_created}")
print(f"颗粒半径: {particle_radius*1e6:.1f} μm")
print(f"计算域尺寸: {cell_size[0]*1e6:.0f} × {cell_size[1]*1e6:.0f} × {cell_size[2]*1e6:.0f} μm")
print(f"周期性单元: {O.cell.hSize}")
print(f"时间步长: {O.dt:.2e} s")
print("请在Controller中手动开始模拟，观察重力堆积过程")
print("建议：可以在3D View中调整视角，观察颗粒的周期性边界行为")
